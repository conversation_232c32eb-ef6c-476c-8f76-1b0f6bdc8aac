body {
  background-color: #f3f4f6;
  color: #111827;
}

.main-card {
  max-width: 72rem;
  margin: 2rem auto;
  padding: 2rem;
  background: #fff;
  color: #222;
  border-radius: 0.75rem;
  box-shadow: 0 2px 16px 0 rgba(0,0,0,0.08);
}

/* Dark mode styles */
@media (prefers-color-scheme: dark) {
  body:not(.light-mode) {
    background-color: #111827;
    color: #f9fafb;
  }
  body:not(.light-mode) .main-card {
    background: #23272f;
    color: #fff;
  }
}

body.dark-mode {
  background-color: #111827;
  color: #f9fafb;
}

body.dark-mode .main-card {
  background: #23272f;
  color: #fff;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
}

.title-container {
  position: relative;
}

.title {
  font-size: 2.25rem;
  font-weight: bold;
  margin: 0;
  position: relative;
  cursor: pointer;
  transition: color 0.2s ease;
}

.title:hover {
  color: #3b82f6;
}

/* Title hover dark mode styles */
@media (prefers-color-scheme: dark) {
  body:not(.light-mode) .title:hover {
    color: #60a5fa;
  }
}

body.dark-mode .title:hover {
  color: #60a5fa;
}

.info-panel {
  position: absolute;
  top: 100%;
  left: 50%;
  transform: translateX(-50%);
  margin-top: 0.5rem;
  background: #fff;
  border: 1px solid #d1d5db;
  border-radius: 0.5rem;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  padding: 1rem;
  width: 280px;
  opacity: 0;
  visibility: hidden;
  pointer-events: none;
  transition: opacity 0.2s ease, visibility 0.2s ease, transform 0.2s ease;
  transform: translateX(-50%) translateY(-10px);
  z-index: 1000;
}

.info-panel-content h3 {
  margin: 0 0 0.5rem 0;
  font-size: 1.125rem;
  font-weight: 600;
  color: #111827;
}

.info-panel-content p {
  margin: 0;
  font-size: 0.875rem;
  line-height: 1.5;
  color: #6b7280;
}

.info-panel-content p + p {
  margin-top: 0.5rem;
}

.title:hover .info-panel {
  opacity: 1;
  visibility: visible;
  pointer-events: auto;
  transform: translateX(-50%) translateY(0);
}

/* Info panel dark mode styles */
@media (prefers-color-scheme: dark) {
  body:not(.light-mode) .info-panel {
    background: #374151;
    border-color: #4b5563;
  }
  body:not(.light-mode) .info-panel-content h3 {
    color: #f9fafb;
  }
  body:not(.light-mode) .info-panel-content p {
    color: #d1d5db;
  }
}

body.dark-mode .info-panel {
  background: #374151;
  border-color: #4b5563;
}

body.dark-mode .info-panel-content h3 {
  color: #f9fafb;
}

body.dark-mode .info-panel-content p {
  color: #d1d5db;
}

.settings-menu {
  position: relative;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.settings-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 0.75rem;
  border-radius: 0.5rem;
  background: #fff;
  color: #222;
  border: 1px solid #d1d5db;
  box-shadow: 0 1px 4px 0 rgba(0,0,0,0.04);
  font-size: 1rem;
  cursor: pointer;
  transition: background 0.15s, color 0.15s, box-shadow 0.15s;
}

.settings-btn:not(.bg-blue-500):hover, .settings-btn:not(.bg-blue-500):focus {
  background: #e5e7eb;
  color: #111;
  outline: none;
}

.settings-btn.bg-blue-500 {
  background: #2563eb;
  color: #fff;
  border-color: #2563eb;
}

.settings-btn.text-white {
  color: #fff;
}

/* Settings button dark mode styles */
@media (prefers-color-scheme: dark) {
  body:not(.light-mode) .settings-btn {
    background: #23272f;
    color: #fff;
    border: 1px solid #374151;
  }
  body:not(.light-mode) .settings-btn:not(.bg-blue-500):hover,
  body:not(.light-mode) .settings-btn:not(.bg-blue-500):focus {
    background: #374151;
    color: #fff;
  }
}

body.dark-mode .settings-btn {
  background: #23272f;
  color: #fff;
  border: 1px solid #374151;
}

body.dark-mode .settings-btn:not(.bg-blue-500):hover,
body.dark-mode .settings-btn:not(.bg-blue-500):focus {
  background: #374151;
  color: #fff;
}

/* Ensure blue active state shows in dark mode */
body.dark-mode .settings-btn.bg-blue-500 {
  background: #2563eb !important;
  color: #fff !important;
  border-color: #2563eb !important;
}

@media (prefers-color-scheme: dark) {
  body:not(.light-mode) .settings-btn.bg-blue-500 {
    background: #2563eb !important;
    color: #fff !important;
    border-color: #2563eb !important;
  }
}

.settings-panel {
  display: grid;
  grid-template-columns: repeat(3, 1fr) auto;
  grid-template-rows: repeat(2, 1fr);
  gap: 0.375rem;
  position: absolute;
  right: 0;
  top: 0;
  opacity: 0;
  pointer-events: none;
  align-items: center;
  transition: all 0.3s ease-out;
  background: transparent;
  z-index: 200;
  transform: translateX(100%);
}

.settings-panel.opacity-100.pointer-events-auto {
  opacity: 1;
  pointer-events: auto;
  transform: translateX(0);
}

/* Close button positioning - stays in original settings button position */
#settingsCloseBtn {
  grid-column: 4;
  grid-row: 1;
  justify-self: end;
}

/* Individual button animations - staggered slide-in effect */
.settings-panel .settings-btn:not(#settingsCloseBtn) {
  transition: all 0.3s ease-out;
  transform: translateX(50px);
  opacity: 0;
}

.settings-panel.opacity-100.pointer-events-auto .settings-btn:not(#settingsCloseBtn) {
  transform: translateX(0);
  opacity: 1;
}

/* Staggered animation delays for smooth slide-in */
.settings-panel.opacity-100.pointer-events-auto #scrambleLengthBtn {
  transition-delay: 0.05s;
}

.settings-panel.opacity-100.pointer-events-auto #shortcutsBtn {
  transition-delay: 0.1s;
}

.settings-panel.opacity-100.pointer-events-auto #darkModeBtn {
  transition-delay: 0.15s;
}

.settings-panel.opacity-100.pointer-events-auto #diagramBtn {
  transition-delay: 0.2s;
}

.settings-panel.opacity-100.pointer-events-auto #inspectionBtn {
  transition-delay: 0.25s;
}

.settings-panel.opacity-100.pointer-events-auto #connectBtn {
  transition-delay: 0.3s;
}

/* Hide title container when settings menu is open */
.header.settings-open .title-container {
  opacity: 0;
  visibility: hidden;
}

.title-container {
  transition: opacity 0.3s ease, visibility 0.3s ease;
}

.icon {
  display: flex;
  align-items: center;
  font-size: 1.25rem;
  line-height: 1;
}

.label {
  display: inline-block;
  vertical-align: middle;
  font-size: 1rem;
}

/* Tailwind utility overrides for dark mode */
@media (prefers-color-scheme: dark) {
  body:not(.light-mode) .bg-gray-100 { background-color: #374151; }
  body:not(.light-mode) .bg-gray-800 { background-color: #23272f; }
  body:not(.light-mode) .bg-white { background-color: #23272f; }
  body:not(.light-mode) .text-gray-700 { color: #d1d5db; }
  body:not(.light-mode) .text-gray-300 { color: #9ca3af; }
  body:not(.light-mode) .text-gray-600 { color: #9ca3af; }
  body:not(.light-mode) .text-black { color: #f9fafb; }
  body:not(.light-mode) .border-gray-300 { border-color: #4b5563; }
  body:not(.light-mode) .border-gray-600 { border-color: #6b7280; }
  body:not(.light-mode) .hover\\:bg-gray-200:hover { background-color: #374151; }
  body:not(.light-mode) .hover\\:bg-gray-700:hover { background-color: #374151; }
  body:not(.light-mode) .bg-gray-50 { background-color: #4b5563; }
  body:not(.light-mode) .hover\\:text-black:hover { color: #f9fafb; }
}

body.dark-mode .bg-gray-100 { background-color: #374151; }
body.dark-mode .bg-gray-800 { background-color: #23272f; }
body.dark-mode .bg-white { background-color: #23272f; }
body.dark-mode .text-gray-700 { color: #d1d5db; }
body.dark-mode .text-gray-300 { color: #9ca3af; }
body.dark-mode .text-gray-600 { color: #9ca3af; }
body.dark-mode .text-black { color: #f9fafb; }
body.dark-mode .border-gray-300 { border-color: #4b5563; }
body.dark-mode .border-gray-600 { border-color: #6b7280; }
body.dark-mode .hover\\:bg-gray-200:hover { background-color: #374151; }
body.dark-mode .hover\\:bg-gray-700:hover { background-color: #374151; }
body.dark-mode .bg-gray-50 { background-color: #4b5563; }
body.dark-mode .hover\\:text-black:hover { color: #f9fafb; }

/* Action buttons dark mode styles */
@media (prefers-color-scheme: dark) {
  body:not(.light-mode) #refreshBtn,
  body:not(.light-mode) #prevBtn,
  body:not(.light-mode) #exportBtn,
  body:not(.light-mode) #clearSessionBtn {
    background-color: #23272f;
    color: #fff;
    border-color: #374151;
  }

  body:not(.light-mode) #refreshBtn:hover,
  body:not(.light-mode) #prevBtn:hover,
  body:not(.light-mode) #exportBtn:hover,
  body:not(.light-mode) #clearSessionBtn:hover {
    background-color: #374151;
    color: #fff;
  }
}

body.dark-mode #refreshBtn,
body.dark-mode #prevBtn,
body.dark-mode #exportBtn,
body.dark-mode #clearSessionBtn {
  background-color: #23272f;
  color: #fff;
  border-color: #374151;
}

body.dark-mode #refreshBtn:hover,
body.dark-mode #prevBtn:hover,
body.dark-mode #exportBtn:hover,
body.dark-mode #clearSessionBtn:hover {
  background-color: #374151;
  color: #fff;
}

/* SweetAlert2 dark mode styles */
@media (prefers-color-scheme: dark) {
  body:not(.light-mode) .swal2-popup {
    background-color: #23272f !important;
    color: #f9fafb !important;
  }

  body:not(.light-mode) .swal2-title {
    color: #f9fafb !important;
  }

  body:not(.light-mode) .swal2-content,
  body:not(.light-mode) .swal2-html-container {
    color: #d1d5db !important;
  }

  body:not(.light-mode) .swal2-popup:not(.swal-default-colors) .swal2-confirm,
  body:not(.light-mode) .swal2-popup:not(.swal-default-colors) .swal2-deny,
  body:not(.light-mode) .swal2-popup:not(.swal-default-colors) .swal2-cancel {
    background-color: #23272f !important;
    color: #fff !important;
    border: 1px solid #374151 !important;
  }

  body:not(.light-mode) .swal2-popup:not(.swal-default-colors) .swal2-confirm:hover,
  body:not(.light-mode) .swal2-popup:not(.swal-default-colors) .swal2-deny:hover,
  body:not(.light-mode) .swal2-popup:not(.swal-default-colors) .swal2-cancel:hover {
    background-color: #374151 !important;
  }
}

body.dark-mode .swal2-popup {
  background-color: #23272f !important;
  color: #f9fafb !important;
}

body.dark-mode .swal2-title {
  color: #f9fafb !important;
}

body.dark-mode .swal2-content,
body.dark-mode .swal2-html-container {
  color: #d1d5db !important;
}

body.dark-mode .swal2-popup:not(.swal-default-colors) .swal2-confirm,
body.dark-mode .swal2-popup:not(.swal-default-colors) .swal2-deny,
body.dark-mode .swal2-popup:not(.swal-default-colors) .swal2-cancel {
  background-color: #23272f !important;
  color: #fff !important;
  border: 1px solid #374151 !important;
}

body.dark-mode .swal2-popup:not(.swal-default-colors) .swal2-confirm:hover,
body.dark-mode .swal2-popup:not(.swal-default-colors) .swal2-deny:hover,
body.dark-mode .swal2-popup:not(.swal-default-colors) .swal2-cancel:hover {
  background-color: #374151 !important;
}

/* Trend modal styles */
#trendModal select {
  transition: background-color 0.2s ease, border-color 0.2s ease;
}

#trendModal select:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

@media (prefers-color-scheme: dark) {
  body:not(.light-mode) #trendModal select {
    background-color: #374151;
    border-color: #4b5563;
    color: #f9fafb;
  }
}

body.dark-mode #trendModal select {
  background-color: #374151;
  border-color: #4b5563;
  color: #f9fafb;
}

.swal2-actions {
  display: flex;
  justify-content: center;
  gap: 0.5rem;
  flex-wrap: wrap;
}