<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>David's timer</title>
  <link rel="stylesheet" href="style.css">
  <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
  <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.2/css/all.min.css" rel="stylesheet">
  <link href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css" rel="stylesheet">
  <link rel="icon" type="image/svg+xml" href="data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'%3E%3Crect width='100' height='100' fill='white'/%3E%3Cg stroke='black' stroke-width='2'%3E%3Crect x='5' y='5' width='28' height='28' fill='%23ff0000'/%3E%3Crect x='36' y='5' width='28' height='28' fill='%23ff8000'/%3E%3Crect x='67' y='5' width='28' height='28' fill='%23ffff00'/%3E%3Crect x='5' y='36' width='28' height='28' fill='%2300ff00'/%3E%3Crect x='36' y='36' width='28' height='28' fill='%230000ff'/%3E%3Crect x='67' y='36' width='28' height='28' fill='%23ffffff'/%3E%3Crect x='5' y='67' width='28' height='28' fill='%2300ff00'/%3E%3Crect x='36' y='67' width='28' height='28' fill='%23ff0000'/%3E%3Crect x='67' y='67' width='28' height='28' fill='%230000ff'/%3E%3C/g%3E%3C/svg%3E">
  <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
  <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
  <script defer src="script.js"></script>
</head>
<body class="bg-gray-100 text-gray-900 transition-colors" id="body">
  <div class="main-card">
    <div class="header">
      <div class="title-container">
        <h1 class="title" title="About">
          David's timer
          <div id="infoPanel" class="info-panel">
            <div class="info-panel-content">
              <h3>David's timer</h3>
              <p>A modern speedcubing timer with statistics tracking, inspection mode, and Bluetooth connectivity</p>
              <p>&copy; <?php echo date("Y"); ?> David Goldberg</p>
            </div>
          </div>
        </h1>
      </div>
      <div class="settings-menu" id="settingsMenu" data-open="false">
        <button id="settingsBtn" class="settings-btn shadow" title="Settings">
          <span class="icon"><i class="fa-solid fa-gear"></i></span>
          <span class="label hidden sm:inline">Settings</span>
        </button>
        <div id="settingsPanel" class="settings-panel">
          <button id="scrambleLengthBtn" class="settings-btn shadow" title="Scramble">
            <span class="icon"><i class="fa-solid fa-sliders"></i></span><span class="label hidden sm:inline">Scramble</span>
          </button>
          <button id="shortcutsBtn" class="settings-btn shadow" title="Shortcuts">
            <span class="icon"><i class="fa-solid fa-keyboard"></i></span><span class="label hidden sm:inline">Shortcuts</span>
          </button>
          <button id="darkModeBtn" class="settings-btn shadow" title="Dark mode">
            <span class="icon"><i id="darkModeIcon" class="fa-solid fa-moon"></i></span><span class="label hidden sm:inline">Dark</span>
          </button>
          <button id="diagramBtn" class="settings-btn shadow" title="Diagram">
            <span class="icon"><i class="fa-solid fa-cube"></i></span><span class="label hidden sm:inline">Diagram</span>
          </button>
          <button id="inspectionBtn" class="settings-btn shadow" title="Inspection">
            <span class="icon"><i class="fa-solid fa-eye"></i></span><span class="label hidden sm:inline">Inspection</span>
          </button>
          <button id="connectBtn" class="settings-btn shadow" title="Connect">
            <span class="icon"><i class="fa-brands fa-bluetooth"></i></span><span class="label hidden sm:inline">Connect</span>
          </button>
          <button id="settingsCloseBtn" class="settings-btn shadow" title="Close">
            <span class="icon"><i class="fa-solid fa-xmark"></i></span><span class="label hidden sm:inline">Close</span>
          </button>
        </div>
      </div>
    </div>
    <div class="flex justify-center items-center gap-4 mb-4">
      <div class="relative w-28 h-28">
        <img id="scrambleImg" src="" alt="Scramble" class="absolute top-0 left-0 w-full h-full object-contain hidden" />
        <div id="scrambleSpinner" class="absolute top-0 left-0 w-full h-full flex items-center justify-center bg-gray-100 dark:bg-gray-800 rounded border">
          <svg class="animate-spin w-6 h-6 text-gray-500" viewBox="0 0 24 24" fill="none">
            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4z"></path>
          </svg>
        </div>
      </div>
      <div id="scramble" class="text-2xl font-mono"></div>
      <div class="flex gap-2">
        <button id="prevBtn" class="p-2 rounded-md hover:bg-gray-200 dark:hover:bg-gray-700 border border-gray-300 dark:border-gray-600 shadow disabled:opacity-50 disabled:cursor-not-allowed disabled:hover:bg-white dark:disabled:hover:bg-gray-800" title="Previous" disabled>
          <i class="fa-solid fa-circle-left w-5 h-5"></i><span class="hidden sm:inline"> Previous</span>
        </button>
        <button id="refreshBtn" class="p-2 rounded-md hover:bg-gray-200 dark:hover:bg-gray-700 border border-gray-300 dark:border-gray-600 shadow" title="Next">
          <i class="fa-solid fa-circle-right w-5 h-5"></i><span class="hidden sm:inline"> Next</span>
        </button>
      </div>
    </div>
    <div class="text-center text-6xl font-mono font-semibold mb-6" id="timerArea">
      <span id="latestTime">--:--.--</span>
    </div>
    <div class="space-y-4" id="session">
      <div class="rounded-md bg-white dark:bg-gray-800 py-4">
        <div class="flex justify-between items-center mb-2">
          <h2 class="text-xl font-semibold">Statistics</h2>
          <div class="flex gap-2">
            <button id="exportBtn" class="p-2 rounded-md hover:bg-gray-200 dark:hover:bg-gray-700 border border-gray-300 dark:border-gray-600 shadow" title="Export">
              <i class="fa-solid fa-file-export w-5 h-5"></i><span class="hidden sm:inline"> Export</span>
            </button>
            <button id="clearSessionBtn" class="p-2 rounded-md hover:bg-gray-200 dark:hover:bg-gray-700 border border-gray-300 dark:border-gray-600 shadow" title="Clear">
              <i class="fa-solid fa-trash w-5 h-5"></i><span class="hidden sm:inline"> Clear</span>
            </button>
          </div>
        </div>
        <div class="flex gap-2 text-md mb-2 border-b border-gray-300 dark:border-gray-600">
          <button id="tab-current" class="py-1 px-3 border-b-2 border-blue-500 text-black dark:text-white">
            <i class="fa-solid fa-stopwatch w-5 h-5"></i><span class="hidden sm:inline"> Current</span>
          </button>
          <button id="tab-best" class="py-1 px-3 border-b-2 border-transparent text-gray-600 dark:text-gray-300 hover:text-black dark:hover:text-white">
            <i class="fa-solid fa-trophy w-5 h-5"></i><span class="hidden sm:inline"> Session best</span>
          </button>
          <button id="tab-trend" class="py-1 px-3 border-b-2 border-transparent text-gray-600 dark:text-gray-300 hover:text-black dark:hover:text-white">
            <i class="fa-solid fa-chart-simple w-5 h-5"></i><span class="hidden sm:inline"> Time distribution</span>
          </button>
        </div>
        <ul class="grid grid-flow-col auto-rows-fr grid-rows-2 gap-2" id="averagesList">
          <li data-group="current">Single: <span id="single">--</span></li>
          <li data-group="best">Single: <span id="bestSingle">--</span></li>
        
          <li data-group="current">Ao5: <span id="ao5">--</span></li>
          <li data-group="best">Ao5: <span id="bestAo5">--</span></li>
        
          <li data-group="current">Ao12: <span id="ao12">--</span></li>
          <li data-group="best">Ao12: <span id="bestAo12">--</span></li>
        
          <li data-group="current">Ao100: <span id="ao100">--</span></li>
          <li data-group="best">Ao100: <span id="bestAo100">--</span></li>
        
          <li data-group="current">Ao1000: <span id="ao1000">--</span></li>
          <li data-group="best">Ao1000: <span id="bestAo1000">--</span></li>
        </ul>
      </div>
      <div class="flex justify-between items-center mb-2">
        <h2 class="text-xl font-semibold">Solves</h2>
      </div>
      <div class="max-h-48 overflow-y-auto rounded-md bg-white dark:bg-gray-800 py-4">
        <ul id="timesList" class="space-y-4 text-md"></ul>
      </div>
    </div>
  </div>
  <div id="trendModal" class="fixed inset-0 bg-black/50 flex items-center justify-center z-50 hidden">
    <div class="bg-white dark:bg-gray-800 p-6 rounded-md shadow-md w-11/12 max-w-4xl h-5/6 max-h-screen overflow-hidden">
      <div class="flex justify-between items-center mb-4">
        <h2 class="text-xl font-semibold text-gray-700 dark:text-gray-300">Time distribution</h2>
        <div class="flex items-center gap-4">
          <label for="trendSelect" class="text-sm font-medium text-gray-700 dark:text-gray-300">Distribution type:</label>
          <select id="trendSelect" class="px-3 py-1 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white">
            <option value="single">Single</option>
            <option value="5" selected>Ao5</option>
            <option value="12">Ao12</option>
            <option value="100">Ao100</option>
            <option value="1000">Ao1000</option>
          </select>
          <button id="trendModalClose" class="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200">
            <i class="fa-solid fa-xmark text-xl"></i>
          </button>
        </div>
      </div>
      <div class="h-full pb-16">
        <canvas id="trendChart" class="w-full h-full"></canvas>
      </div>
    </div>
  </div>
</body>
</html>